import React, { createContext, useContext, useState, useEffect } from 'react';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  companyId: string;
  companyName: string;
  role: 'user' | 'admin' | 'super_admin';
}

export interface Company {
  id: string;
  name: string;
  domain?: string;
  logoUrl?: string;
  contactEmail: string;
}

interface AuthContextType {
  user: User | null;
  company: Company | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Mock data for demonstration
  const mockUsers: (User & { password: string })[] = [
    {
      id: '1',
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'System',
      lastName: 'Admin',
      companyId: 'system',
      companyName: 'System',
      role: 'super_admin',
    },
    {
      id: '2',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'John',
      lastName: 'Doe',
      companyId: '1',
      companyName: 'Acme Solar Solutions',
      role: 'admin',
    },
    {
      id: '3',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Smith',
      companyId: '1',
      companyName: 'Acme Solar Solutions',
      role: 'user',
    },
  ];

  const mockCompanies: Company[] = [
    {
      id: '1',
      name: 'Acme Solar Solutions',
      domain: 'acme-solar.com',
      contactEmail: '<EMAIL>',
    },
    {
      id: '2',
      name: 'Green Energy Corp',
      domain: 'greenenergy.com',
      contactEmail: '<EMAIL>',
    },
  ];

  useEffect(() => {
    // Check for existing session on app load
    const checkSession = async () => {
      try {
        // In a real app, this would check for a valid JWT token
        const savedUser = localStorage.getItem('user');
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          
          // Set company data if user is not super admin
          if (userData.companyId !== 'system') {
            const userCompany = mockCompanies.find(c => c.id === userData.companyId);
            if (userCompany) {
              setCompany(userCompany);
            }
          }
        }
      } catch (error) {
        console.error('Error checking session:', error);
        localStorage.removeItem('user');
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Mock authentication - replace with actual Supabase auth later
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const foundUser = mockUsers.find(u => u.email === email && u.password === password);
      
      if (!foundUser) {
        throw new Error('Invalid email or password');
      }

      const { password: _, ...userWithoutPassword } = foundUser;
      setUser(userWithoutPassword);
      
      // Set company data if user is not super admin
      if (userWithoutPassword.companyId !== 'system') {
        const userCompany = mockCompanies.find(c => c.id === userWithoutPassword.companyId);
        if (userCompany) {
          setCompany(userCompany);
        }
      }
      
      // Save to localStorage (in real app, this would be handled by Supabase)
      localStorage.setItem('user', JSON.stringify(userWithoutPassword));
      
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setCompany(null);
    localStorage.removeItem('user');
  };

  const updateUser = (updatedUser: User) => {
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  const value: AuthContextType = {
    user,
    company,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    updateUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
