import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Plus, Building2, Edit, Trash2, Globe } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

const companySchema = z.object({
  name: z.string().min(2, "Company name must be at least 2 characters"),
  domain: z.string().optional(),
  logoUrl: z.string().url().optional().or(z.literal("")),
  contactEmail: z.string().email("Please enter a valid email address"),
});

type CompanyFormData = z.infer<typeof companySchema>;

interface Company {
  id: string;
  name: string;
  domain?: string;
  logoUrl?: string;
  contactEmail: string;
  createdAt: string;
  userCount: number;
}

// Mock data for demonstration
const mockCompanies: Company[] = [
  {
    id: "1",
    name: "Acme Solar Solutions",
    domain: "acme-solar.com",
    logoUrl: "",
    contactEmail: "<EMAIL>",
    createdAt: "2024-01-15",
    userCount: 3,
  },
  {
    id: "2",
    name: "Green Energy Corp",
    domain: "greenenergy.com",
    logoUrl: "",
    contactEmail: "<EMAIL>",
    createdAt: "2024-02-20",
    userCount: 5,
  },
];

export function CompanyManagement() {
  const [companies, setCompanies] = useState<Company[]>(mockCompanies);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CompanyFormData>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: "",
      domain: "",
      logoUrl: "",
      contactEmail: "",
    },
  });

  const handleSubmit = async (data: CompanyFormData) => {
    setIsLoading(true);
    
    try {
      // Mock API call - replace with actual Supabase call later
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (editingCompany) {
        // Update existing company
        setCompanies(prev => prev.map(company => 
          company.id === editingCompany.id 
            ? { ...company, ...data }
            : company
        ));
      } else {
        // Create new company
        const newCompany: Company = {
          id: Date.now().toString(),
          ...data,
          createdAt: new Date().toISOString().split('T')[0],
          userCount: 0,
        };
        setCompanies(prev => [...prev, newCompany]);
      }
      
      setIsDialogOpen(false);
      setEditingCompany(null);
      form.reset();
    } catch (error) {
      console.error("Error saving company:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (company: Company) => {
    setEditingCompany(company);
    form.reset({
      name: company.name,
      domain: company.domain || "",
      logoUrl: company.logoUrl || "",
      contactEmail: company.contactEmail,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (companyId: string) => {
    if (confirm("Are you sure you want to delete this company? This action cannot be undone.")) {
      setCompanies(prev => prev.filter(company => company.id !== companyId));
    }
  };

  const openCreateDialog = () => {
    setEditingCompany(null);
    form.reset();
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Companies</h3>
          <p className="text-sm text-muted-foreground">
            {companies.length} companies registered
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Company
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingCompany ? "Edit Company" : "Create New Company"}
              </DialogTitle>
              <DialogDescription>
                {editingCompany 
                  ? "Update the company information below."
                  : "Add a new client company to the system."
                }
              </DialogDescription>
            </DialogHeader>
            
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter company name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Email</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="domain"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Domain (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="company.com" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="logoUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo URL (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com/logo.png" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex gap-2 pt-4">
                  <Button type="submit" disabled={isLoading} className="flex-1">
                    {isLoading ? "Saving..." : editingCompany ? "Update" : "Create"}
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsDialogOpen(false)}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Users</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {companies.map((company) => (
              <TableRow key={company.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                      <Building2 className="h-4 w-4 text-white" />
                    </div>
                    <div>
                      <div className="font-medium">{company.name}</div>
                      {company.domain && (
                        <div className="text-sm text-muted-foreground flex items-center gap-1">
                          <Globe className="h-3 w-3" />
                          {company.domain}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{company.contactEmail}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{company.userCount} users</Badge>
                </TableCell>
                <TableCell>{company.createdAt}</TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center gap-2 justify-end">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(company)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(company.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
}
