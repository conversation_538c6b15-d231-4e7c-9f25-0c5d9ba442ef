import { Building2, LogOut, User } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MetricsVisibilityPanel } from "./MetricsVisibilityPanel";

interface DashboardHeaderProps {
  visibleMetrics?: Record<string, boolean>;
  onToggleMetric?: (metric: string) => void;
}

export function DashboardHeader({
  visibleMetrics,
  onToggleMetric
}: DashboardHeaderProps) {
  const { user, company, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };
  return (
    <header className="w-full bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {company?.logoUrl ? (
            <img
              src={company.logoUrl}
              alt={`${company.name} Logo`}
              className="h-12 w-12 rounded-lg object-cover"
              onError={(e) => {
                // Fallback to icon if image fails to load
                const fallback = document.createElement('div');
                fallback.className = 'h-12 w-12 bg-gradient-primary rounded-lg flex items-center justify-center';
                fallback.innerHTML = '<svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>';
                (e.target as HTMLImageElement).parentNode?.replaceChild(fallback, e.target as HTMLImageElement);
              }}
            />
          ) : (
            <div className="h-12 w-12 bg-gradient-primary rounded-lg flex items-center justify-center">
              <Building2 className="h-6 w-6 text-white" />
            </div>
          )}
          <div>
            <h1 className="text-xl font-bold text-foreground">
              {company?.name || 'Dashboard'}
            </h1>
            <p className="text-sm text-muted-foreground">Analytics Dashboard</p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {visibleMetrics && onToggleMetric && (
            <MetricsVisibilityPanel
              visibleMetrics={visibleMetrics}
              onToggleMetric={onToggleMetric}
            />
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-gradient-primary text-white">
                    {user?.firstName?.[0]}{user?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.firstName} {user?.lastName}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}